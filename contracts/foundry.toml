[profile.default]
src = "src"
out = "out"
libs = ["lib"]
evm_version = 'cancun'
optimizer = true
optimizer_runs = 200
ignored_error_codes = [3860, 5574] # contract-size
auto_detect_remappings = false
exclude = [
    "lib/V2-gov/test/**",
    "lib/V2-gov/script/**",
    "lib/V2-gov/lib/**",
    "lib/openzeppelin-contracts/test/**",
    "lib/openzeppelin-contracts/certora/**",
    "lib/openzeppelin-contracts/lib/**",
    "lib/openzeppelin-contracts/contracts/mocks/**",
    "lib/forge-std/test/**",
    "lib/Solady/test/**"
]
remappings = [
    "forge-std/=lib/forge-std/src/",
    "openzeppelin-contracts/=lib/openzeppelin-contracts/",
    "openzeppelin/=lib/V2-gov/lib/openzeppelin-contracts/",
    "Solady/=lib/Solady/src/",
    "V2-gov/=lib/V2-gov/"
]
fs_permissions = [
    { access = "read", path = "./utils/assets/" },
    { access = "read-write", path = "./utils/assets/test_output" },
    { access = "read-write", path = "./deployment-manifest.json" },
    { access = "read", path = "./addresses/" }
]

[invariant]
call_override = false
fail_on_revert = true
runs = 500
depth = 50
# failure_persist_dir = "/dev/null" # XXX circumvent this half-baked Foundry feature
shrink_run_limit = 0 # XXX shrinking is super broken, results in completely wrong repro sequence

[profile.ci.invariant]
shrink_run_limit = 0 # takes too damn long to shrink, don't waste Github minutes

[profile.default.rpc_endpoints]
mainnet = "${MAINNET_RPC_URL}"
tenderly-mainnet = "${TENDERLY_RPC_URL}"

[profile.e2e]
# Allows us to .gitignore broadcast logs of E2E deployments
broadcast = 'broadcast-e2e'
# As we are running tests in a fork that uses the same chain ID as the forked chain, it is important not to be caching
# any storage, as any new state on top of the forked block is ephemeral and should not be commingled with real state.
# Anvil is going to be caching any storage requests that hit the underlying RPC anyway.
no_storage_caching = true

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
